int attr coordinatorLayoutStyle 0x0
int attr keylines 0x0
int attr layout_anchor 0x0
int attr layout_anchorGravity 0x0
int attr layout_behavior 0x0
int attr layout_dodgeInsetEdges 0x0
int attr layout_insetEdge 0x0
int attr layout_keyline 0x0
int attr statusBarBackground 0x0
int id bottom 0x0
int id end 0x0
int id left 0x0
int id none 0x0
int id right 0x0
int id start 0x0
int id top 0x0
int style Widget_Support_CoordinatorLayout 0x0
int[] styleable CoordinatorLayout { 0x0, 0x0 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
