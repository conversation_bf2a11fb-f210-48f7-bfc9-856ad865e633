﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Machine>YHYASOFT</Machine>
    <WindowsUser>yhyasoft</WindowsUser>
    <TargetPlatformIdentifier>Windows</TargetPlatformIdentifier>
    <TargetOsVersion>10.0</TargetOsVersion>
    <TargetOsDescription>Windows 10.0</TargetOsDescription>
    <SolutionConfiguration>Debug|AnyCPU</SolutionConfiguration>
    <PackageArchitecture>x64</PackageArchitecture>
    <PackageIdentityName>com.companyname.unitedprinting</PackageIdentityName>
    <PackageIdentityPublisher>CN=User Name</PackageIdentityPublisher>
    <IntermediateOutputPath>C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\</IntermediateOutputPath>
    <RemoteDeploymentType>CopyToDevice</RemoteDeploymentType>
    <PackageRegistrationPath></PackageRegistrationPath>
    <RemoveNonLayoutFiles>false</RemoveNonLayoutFiles>
    <DeployOptionalPackages>false</DeployOptionalPackages>
    <WindowsSdkPath>C:\Program Files %28x86%29\Windows Kits\10\</WindowsSdkPath>
    <LayoutDir>C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\bin\Debug\net8.0-windows10.0.19041.0\win10-x64\AppX</LayoutDir>
  </PropertyGroup>
  <ItemGroup>
    <AppXManifest Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\bin\Debug\net8.0-windows10.0.19041.0\win10-x64\AppxManifest.xml">
      <PackagePath>AppxManifest.xml</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
    </AppXManifest>
  </ItemGroup>
  <ItemGroup>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\bin\Debug\net8.0-windows10.0.19041.0\win10-x64\resources.pri">
      <PackagePath>resources.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\bin\Debug\net8.0-windows10.0.19041.0\win10-x64\unitedprinting.runtimeconfig.json">
      <PackagePath>unitedprinting.runtimeconfig.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\bin\Debug\net8.0-windows10.0.19041.0\win10-x64\unitedprinting.dll">
      <PackagePath>unitedprinting.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\apphost.exe">
      <PackagePath>unitedprinting.exe</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\Resources\Raw\AboutAssets.txt">
      <PackagePath>AboutAssets.txt</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-100.png">
      <PackagePath>splashSplashScreen.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-125.png">
      <PackagePath>splashSplashScreen.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-150.png">
      <PackagePath>splashSplashScreen.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-200.png">
      <PackagePath>splashSplashScreen.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-400.png">
      <PackagePath>splashSplashScreen.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\f\OpenSans-Regular.ttf">
      <PackagePath>OpenSans-Regular.ttf</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\f\OpenSans-Semibold.ttf">
      <PackagePath>OpenSans-Semibold.ttf</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appicon.ico">
      <PackagePath>appicon.ico</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-100.png">
      <PackagePath>appiconLargeTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-125.png">
      <PackagePath>appiconLargeTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-150.png">
      <PackagePath>appiconLargeTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-200.png">
      <PackagePath>appiconLargeTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-400.png">
      <PackagePath>appiconLargeTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-16.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-16.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-24.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-24.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-256.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-256.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-32.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-32.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-48.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-48.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-16.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-16.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-24.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-24.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-256.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-256.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-32.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-32.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-48.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-48.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-100.png">
      <PackagePath>appiconLogo.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-125.png">
      <PackagePath>appiconLogo.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-150.png">
      <PackagePath>appiconLogo.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-200.png">
      <PackagePath>appiconLogo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-400.png">
      <PackagePath>appiconLogo.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-16.png">
      <PackagePath>appiconLogo.targetsize-16.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-24.png">
      <PackagePath>appiconLogo.targetsize-24.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-256.png">
      <PackagePath>appiconLogo.targetsize-256.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-32.png">
      <PackagePath>appiconLogo.targetsize-32.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-48.png">
      <PackagePath>appiconLogo.targetsize-48.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-100.png">
      <PackagePath>appiconMediumTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-125.png">
      <PackagePath>appiconMediumTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-150.png">
      <PackagePath>appiconMediumTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-200.png">
      <PackagePath>appiconMediumTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-400.png">
      <PackagePath>appiconMediumTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-100.png">
      <PackagePath>appiconSmallTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-125.png">
      <PackagePath>appiconSmallTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-150.png">
      <PackagePath>appiconSmallTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-200.png">
      <PackagePath>appiconSmallTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-400.png">
      <PackagePath>appiconSmallTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-100.png">
      <PackagePath>appiconStoreLogo.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-125.png">
      <PackagePath>appiconStoreLogo.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-150.png">
      <PackagePath>appiconStoreLogo.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-200.png">
      <PackagePath>appiconStoreLogo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-400.png">
      <PackagePath>appiconStoreLogo.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-100.png">
      <PackagePath>appiconWideTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-125.png">
      <PackagePath>appiconWideTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-150.png">
      <PackagePath>appiconWideTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-200.png">
      <PackagePath>appiconWideTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-400.png">
      <PackagePath>appiconWideTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-100.png">
      <PackagePath>dotnet_bot.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-125.png">
      <PackagePath>dotnet_bot.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-150.png">
      <PackagePath>dotnet_bot.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-200.png">
      <PackagePath>dotnet_bot.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\obj\Debug\net8.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-400.png">
      <PackagePath>dotnet_bot.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprinting\unitedprinting\bin\Debug\net8.0-windows10.0.19041.0\win10-x64\unitedprinting.deps.json">
      <PackagePath>unitedprinting.deps.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\Microsoft.Windows.SDK.NET.dll">
      <PackagePath>Microsoft.Windows.SDK.NET.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\WinRT.Runtime.dll">
      <PackagePath>WinRT.Runtime.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.dll">
      <PackagePath>Microsoft.Extensions.Configuration.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\8.0.1\lib\net8.0\Microsoft.Extensions.DependencyInjection.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\8.0.2\lib\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\8.0.1\lib\net8.0\Microsoft.Extensions.Logging.dll">
      <PackagePath>Microsoft.Extensions.Logging.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.2\lib\net8.0\Microsoft.Extensions.Logging.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Logging.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.debug\8.0.1\lib\net8.0\Microsoft.Extensions.Logging.Debug.dll">
      <PackagePath>Microsoft.Extensions.Logging.Debug.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.2\lib\net8.0\Microsoft.Extensions.Options.dll">
      <PackagePath>Microsoft.Extensions.Options.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\8.0.0\lib\net8.0\Microsoft.Extensions.Primitives.dll">
      <PackagePath>Microsoft.Extensions.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.graphics.win2d\1.2.0\lib\net6.0-windows10.0.19041.0\Microsoft.Graphics.Canvas.Interop.dll">
      <PackagePath>Microsoft.Graphics.Canvas.Interop.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.io.recyclablememorystream\3.0.1\lib\net6.0\Microsoft.IO.RecyclableMemoryStream.dll">
      <PackagePath>Microsoft.IO.RecyclableMemoryStream.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.compatibility\8.0.100\lib\net8.0-windows10.0.19041\Microsoft.Maui.Controls.Compatibility.dll">
      <PackagePath>Microsoft.Maui.Controls.Compatibility.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\Microsoft.Maui.Controls.dll">
      <PackagePath>Microsoft.Maui.Controls.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.xaml\8.0.100\lib\net8.0-windows10.0.19041\Microsoft.Maui.Controls.Xaml.dll">
      <PackagePath>Microsoft.Maui.Controls.Xaml.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.core\8.0.100\lib\net8.0-windows10.0.19041\Microsoft.Maui.dll">
      <PackagePath>Microsoft.Maui.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.essentials\8.0.100\lib\net8.0-windows10.0.19041\Microsoft.Maui.Essentials.dll">
      <PackagePath>Microsoft.Maui.Essentials.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.graphics\8.0.100\lib\net8.0-windows10.0.19041\Microsoft.Maui.Graphics.dll">
      <PackagePath>Microsoft.Maui.Graphics.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.graphics.win2d.winui.desktop\8.0.100\lib\net8.0-windows10.0.19041\Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll">
      <PackagePath>Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.InteractiveExperiences.Projection.dll">
      <PackagePath>Microsoft.InteractiveExperiences.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.WinUI.dll">
      <PackagePath>Microsoft.WinUI.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppLifecycle.Projection.dll">
      <PackagePath>Microsoft.Windows.AppLifecycle.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppNotifications.Builder.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Builder.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.Resources.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Resources.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Management.Deployment.Projection.dll">
      <PackagePath>Microsoft.Windows.Management.Deployment.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.PushNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.PushNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Security.AccessControl.Projection.dll">
      <PackagePath>Microsoft.Windows.Security.AccessControl.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.System.Power.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Power.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.System.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Widgets.Projection.dll">
      <PackagePath>Microsoft.Windows.Widgets.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\lib\net6.0-windows10.0.18362.0\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.Net.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\ar\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ar\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\ca\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ca\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\cs\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>cs\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\da\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>da\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\de\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>de\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\el\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>el\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\es\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>es\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\fi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>fi\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\fr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>fr\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\he\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>he\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\hi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hi\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\hr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hr\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\hu\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hu\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\id\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>id\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\it\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>it\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\ja\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ja\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\ko\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ko\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\ms\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ms\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\nb\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>nb\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\nl\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>nl\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\pl\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pl\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\pt-BR\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pt-BR\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\pt\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pt\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\ro\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ro\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\ru\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ru\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\sk\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>sk\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\sv\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>sv\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\th\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>th\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\tr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>tr\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\uk\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>uk\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\vi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>vi\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\zh-HK\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-HK\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\zh-Hans\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-Hans\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\zh-Hant\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-Hant\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.graphics.win2d\1.2.0\runtimes\win-x64\native\Microsoft.Graphics.Canvas.dll">
      <PackagePath>Microsoft.Graphics.Canvas.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\runtimes\win10-x64\native\Microsoft.WindowsAppRuntime.Bootstrap.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.compatibility\8.0.100\lib\net8.0-windows10.0.19041\Microsoft.Maui.Controls.Compatibility.pri">
      <PackagePath>Microsoft.Maui.Controls.Compatibility.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\8.0.100\lib\net8.0-windows10.0.19041\Microsoft.Maui.Controls.pri">
      <PackagePath>Microsoft.Maui.Controls.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.core\8.0.100\lib\net8.0-windows10.0.19041\Microsoft.Maui.pri">
      <PackagePath>Microsoft.Maui.pri</PackagePath>
    </AppxPackagedFile>
  </ItemGroup>
  <ItemGroup>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.5.msix">
      <Name>Microsoft.WindowsAppRuntime.1.5</Name>
      <Version>5001.214.1843.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.5, MinVersion = 5001.214.1843.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.5.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.5.msix">
      <Name>Microsoft.WindowsAppRuntime.1.5</Name>
      <Version>5001.214.1843.0</Version>
      <Architecture>win32</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.5, MinVersion = 5001.214.1843.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.5.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.5.msix">
      <Name>Microsoft.WindowsAppRuntime.1.5</Name>
      <Version>5001.214.1843.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.5, MinVersion = 5001.214.1843.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.5.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.5.msix">
      <Name>Microsoft.WindowsAppRuntime.1.5</Name>
      <Version>5001.214.1843.0</Version>
      <Architecture>arm64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.5, MinVersion = 5001.214.1843.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.5.240802000\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.5.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
  </ItemGroup>
</Project>
