int attr cardBackgroundColor 0x7f040001
int attr cardCornerRadius 0x7f040002
int attr cardElevation 0x7f040003
int attr cardMaxElevation 0x7f040004
int attr cardPreventCornerOverlap 0x7f040005
int attr cardUseCompatPadding 0x7f040006
int attr cardViewStyle 0x7f040007
int attr contentPadding 0x7f040008
int attr contentPaddingBottom 0x7f040009
int attr contentPaddingLeft 0x7f04000a
int attr contentPaddingRight 0x7f04000b
int attr contentPaddingTop 0x7f04000c
int color cardview_dark_background 0x7f060001
int color cardview_light_background 0x7f060002
int color cardview_shadow_end_color 0x7f060003
int color cardview_shadow_start_color 0x7f060004
int dimen cardview_compat_inset_shadow 0x7f080001
int dimen cardview_default_elevation 0x7f080002
int dimen cardview_default_radius 0x7f080003
int style Base_CardView 0x7f160001
int style CardView 0x7f160002
int style CardView_Dark 0x7f160003
int style CardView_Light 0x7f160004
int[] styleable CardView { 0x1010140, 0x101013f, 0x7f040001, 0x7f040002, 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040008, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c }
int styleable CardView_android_minHeight 0
int styleable CardView_android_minWidth 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
