<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2018 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<selector xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">
  <item android:alpha="0.20" android:color="?attr/colorPrimary" android:state_checked="true"/>
  <!-- R<PERSON>ple has an animation to disappear since we have a different state on the foreground
    remove the ripple to avoid overlap while it transitions out -->
  <item android:color="@android:color/transparent" app:state_dragged="true"/>
  <item android:alpha="0.20" android:color="?attr/colorOnSurface" android:state_checked="false"/>
</selector>
