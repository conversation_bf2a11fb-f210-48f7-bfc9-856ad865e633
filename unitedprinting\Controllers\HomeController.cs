using Microsoft.AspNetCore.Mvc;

namespace unitedprinting.Controllers
{
    public class HomeController : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpPost]
        public IActionResult Contact()
        {
            // Handle contact form submission
            TempData["Message"] = "Contact form will open here";
            return RedirectToAction("Index");
        }

        [HttpPost]
        public IActionResult GetQuote()
        {
            // Handle quote request
            TempData["Message"] = "Quote request form will open here";
            return RedirectToAction("Index");
        }

        [HttpPost]
        public IActionResult BusinessCards()
        {
            // Handle business cards request
            TempData["Message"] = "Business card options will be displayed here";
            return RedirectToAction("Index");
        }

        [HttpPost]
        public IActionResult Flyers()
        {
            // Handle flyers request
            TempData["Message"] = "Flyer and brochure options will be displayed here";
            return RedirectToAction("Index");
        }

        [HttpPost]
        public IActionResult Banners()
        {
            // Handle banners request
            TempData["Message"] = "Banner and sign options will be displayed here";
            return RedirectToAction("Index");
        }

        [HttpPost]
        public IActionResult Design()
        {
            // Handle design services request
            TempData["Message"] = "Design services information will be displayed here";
            return RedirectToAction("Index");
        }

        [HttpPost]
        public IActionResult ViewGallery()
        {
            // Handle gallery view request
            TempData["Message"] = "Full gallery will be displayed here";
            return RedirectToAction("Index");
        }

        public IActionResult Error()
        {
            return View();
        }
    }
}
