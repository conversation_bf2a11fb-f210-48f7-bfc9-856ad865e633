namespace unitedprinting
{
    public partial class MainPage : ContentPage
    {
        public MainPage()
        {
            InitializeComponent();
        }

        // Header Events
        private async void OnContactClicked(object sender, EventArgs e)
        {
            await Display<PERSON><PERSON>t("Contact", "Contact form will open here", "OK");
        }

        // Hero Section Events
        private async void OnGetQuoteClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Quote", "Quote request form will open here", "OK");
        }

        // Service Events
        private async void OnBusinessCardsClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Business Cards", "Business card options will be displayed here", "OK");
        }

        private async void OnFlyersClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Flyers & Brochures", "Flyer and brochure options will be displayed here", "OK");
        }

        private async void OnBannersClicked(object sender, EventArgs e)
        {
            await <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Banners & Signs", "Banner and sign options will be displayed here", "OK");
        }

        private async void OnDesignClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Custom Design", "Design services information will be displayed here", "OK");
        }

        // Gallery Events
        private async void OnViewGalleryClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Gallery", "Full gallery will be displayed here", "OK");
        }

        // Pricing Events
        private async void OnBasicPlanClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Basic Plan", "Basic plan signup will open here", "OK");
        }

        private async void OnProfessionalPlanClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Professional Plan", "Professional plan signup will open here", "OK");
        }

        private async void OnEnterprisePlanClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Enterprise Plan", "Enterprise plan signup will open here", "OK");
        }

        // Contact Form Events
        private async void OnSendMessageClicked(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(NameEntry.Text) ||
                string.IsNullOrWhiteSpace(EmailEntry.Text) ||
                string.IsNullOrWhiteSpace(MessageEditor.Text))
            {
                await DisplayAlert("Error", "Please fill in all required fields", "OK");
                return;
            }

            await DisplayAlert("Success", "Your message has been sent! We'll get back to you soon.", "OK");

            // Clear form
            NameEntry.Text = string.Empty;
            EmailEntry.Text = string.Empty;
            PhoneEntry.Text = string.Empty;
            MessageEditor.Text = string.Empty;
        }

        // Online Ordering Events
        private async void OnUploadDesignClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Upload Design", "File picker will open here", "OK");
        }

        private async void OnTrackOrderClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Track Order", "Order tracking system will open here", "OK");
        }

        private async void OnReorderClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Reorder", "Previous orders will be displayed here", "OK");
        }

        // Footer Events
        private async void OnPrivacyClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Privacy Policy", "Privacy policy will be displayed here", "OK");
        }

        private async void OnTermsClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Terms of Service", "Terms of service will be displayed here", "OK");
        }

        private async void OnSupportClicked(object sender, EventArgs e)
        {
            await DisplayAlert("Support", "Support center will open here", "OK");
        }
    }
}
