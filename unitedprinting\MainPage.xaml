<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="unitedprinting.MainPage"
             Title="United Printing Services">

    <ScrollView>
        <VerticalStackLayout Spacing="0">

            <!-- Header Section -->
            <Grid BackgroundColor="#2C3E50" Padding="20,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <StackLayout Grid.Column="0" Orientation="Horizontal" VerticalOptions="Center">
                    <Image Source="printer_logo.png"
                           HeightRequest="40"
                           WidthRequest="40"
                           Aspect="AspectFit" />
                    <Label Text="United Printing"
                           FontSize="24"
                           FontAttributes="Bold"
                           TextColor="White"
                           VerticalOptions="Center"
                           Margin="10,0,0,0" />
                </StackLayout>

                <Button Grid.Column="1"
                        Text="Contact Us"
                        BackgroundColor="#E74C3C"
                        TextColor="White"
                        CornerRadius="20"
                        Padding="15,8"
                        Clicked="OnContactClicked" />
            </Grid>

            <!-- Hero Section -->
            <Grid BackgroundColor="#34495E" Padding="30,40">
                <VerticalStackLayout HorizontalOptions="Center" Spacing="20">
                    <Label Text="Professional Printing Services"
                           FontSize="32"
                           FontAttributes="Bold"
                           TextColor="White"
                           HorizontalOptions="Center"
                           HorizontalTextAlignment="Center" />

                    <Label Text="Quality prints, fast delivery, competitive prices"
                           FontSize="18"
                           TextColor="#BDC3C7"
                           HorizontalOptions="Center"
                           HorizontalTextAlignment="Center" />

                    <Button Text="Get Quote Now"
                            BackgroundColor="#E74C3C"
                            TextColor="White"
                            FontSize="18"
                            Padding="25,12"
                            CornerRadius="25"
                            Clicked="OnGetQuoteClicked" />
                </VerticalStackLayout>
            </Grid>

            <!-- Services Section -->
            <StackLayout Padding="30,40" BackgroundColor="White">
                <Label Text="Our Services"
                       FontSize="28"
                       FontAttributes="Bold"
                       TextColor="#2C3E50"
                       HorizontalOptions="Center"
                       Margin="0,0,0,30" />

                <Grid RowSpacing="20" ColumnSpacing="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!-- Business Cards -->
                    <Frame Grid.Row="0" Grid.Column="0"
                           BackgroundColor="#ECF0F1"
                           Padding="20"
                           CornerRadius="10"
                           HasShadow="True">
                        <StackLayout Spacing="10">
                            <Image Source="business_card_icon.png"
                                   HeightRequest="50"
                                   Aspect="AspectFit" />
                            <Label Text="Business Cards"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="#2C3E50"
                                   HorizontalOptions="Center" />
                            <Label Text="Professional business cards with premium finishes"
                                   FontSize="14"
                                   TextColor="#7F8C8D"
                                   HorizontalTextAlignment="Center" />
                            <Button Text="Learn More"
                                    BackgroundColor="#3498DB"
                                    TextColor="White"
                                    CornerRadius="15"
                                    Clicked="OnBusinessCardsClicked" />
                        </StackLayout>
                    </Frame>

                    <!-- Flyers & Brochures -->
                    <Frame Grid.Row="0" Grid.Column="1"
                           BackgroundColor="#ECF0F1"
                           Padding="20"
                           CornerRadius="10"
                           HasShadow="True">
                        <StackLayout Spacing="10">
                            <Image Source="flyer_icon.png"
                                   HeightRequest="50"
                                   Aspect="AspectFit" />
                            <Label Text="Flyers & Brochures"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="#2C3E50"
                                   HorizontalOptions="Center" />
                            <Label Text="Eye-catching marketing materials"
                                   FontSize="14"
                                   TextColor="#7F8C8D"
                                   HorizontalTextAlignment="Center" />
                            <Button Text="Learn More"
                                    BackgroundColor="#3498DB"
                                    TextColor="White"
                                    CornerRadius="15"
                                    Clicked="OnFlyersClicked" />
                        </StackLayout>
                    </Frame>

                    <!-- Banners & Signs -->
                    <Frame Grid.Row="1" Grid.Column="0"
                           BackgroundColor="#ECF0F1"
                           Padding="20"
                           CornerRadius="10"
                           HasShadow="True">
                        <StackLayout Spacing="10">
                            <Image Source="banner_icon.png"
                                   HeightRequest="50"
                                   Aspect="AspectFit" />
                            <Label Text="Banners & Signs"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="#2C3E50"
                                   HorizontalOptions="Center" />
                            <Label Text="Large format printing for events and advertising"
                                   FontSize="14"
                                   TextColor="#7F8C8D"
                                   HorizontalTextAlignment="Center" />
                            <Button Text="Learn More"
                                    BackgroundColor="#3498DB"
                                    TextColor="White"
                                    CornerRadius="15"
                                    Clicked="OnBannersClicked" />
                        </StackLayout>
                    </Frame>

                    <!-- Custom Design -->
                    <Frame Grid.Row="1" Grid.Column="1"
                           BackgroundColor="#ECF0F1"
                           Padding="20"
                           CornerRadius="10"
                           HasShadow="True">
                        <StackLayout Spacing="10">
                            <Image Source="design_icon.png"
                                   HeightRequest="50"
                                   Aspect="AspectFit" />
                            <Label Text="Custom Design"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="#2C3E50"
                                   HorizontalOptions="Center" />
                            <Label Text="Professional graphic design services"
                                   FontSize="14"
                                   TextColor="#7F8C8D"
                                   HorizontalTextAlignment="Center" />
                            <Button Text="Learn More"
                                    BackgroundColor="#3498DB"
                                    TextColor="White"
                                    CornerRadius="15"
                                    Clicked="OnDesignClicked" />
                        </StackLayout>
                    </Frame>
                </Grid>
            </StackLayout>

            <!-- Gallery Section -->
            <StackLayout Padding="30,40" BackgroundColor="#F8F9FA">
                <Label Text="Our Work Gallery"
                       FontSize="28"
                       FontAttributes="Bold"
                       TextColor="#2C3E50"
                       HorizontalOptions="Center"
                       Margin="0,0,0,30" />

                <Grid RowSpacing="15" ColumnSpacing="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="150" />
                        <RowDefinition Height="150" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <Frame Grid.Row="0" Grid.Column="0" Padding="0" CornerRadius="10" HasShadow="True">
                        <Image Source="gallery1.jpg" Aspect="AspectFill" />
                    </Frame>
                    <Frame Grid.Row="0" Grid.Column="1" Padding="0" CornerRadius="10" HasShadow="True">
                        <Image Source="gallery2.jpg" Aspect="AspectFill" />
                    </Frame>
                    <Frame Grid.Row="0" Grid.Column="2" Padding="0" CornerRadius="10" HasShadow="True">
                        <Image Source="gallery3.jpg" Aspect="AspectFill" />
                    </Frame>
                    <Frame Grid.Row="1" Grid.Column="0" Padding="0" CornerRadius="10" HasShadow="True">
                        <Image Source="gallery4.jpg" Aspect="AspectFill" />
                    </Frame>
                    <Frame Grid.Row="1" Grid.Column="1" Padding="0" CornerRadius="10" HasShadow="True">
                        <Image Source="gallery5.jpg" Aspect="AspectFill" />
                    </Frame>
                    <Frame Grid.Row="1" Grid.Column="2" Padding="0" CornerRadius="10" HasShadow="True">
                        <Image Source="gallery6.jpg" Aspect="AspectFill" />
                    </Frame>
                </Grid>

                <Button Text="View Full Gallery"
                        BackgroundColor="#9B59B6"
                        TextColor="White"
                        CornerRadius="20"
                        Margin="0,20,0,0"
                        Clicked="OnViewGalleryClicked" />
            </StackLayout>

            <!-- Pricing Section -->
            <StackLayout Padding="30,40" BackgroundColor="White">
                <Label Text="Pricing Plans"
                       FontSize="28"
                       FontAttributes="Bold"
                       TextColor="#2C3E50"
                       HorizontalOptions="Center"
                       Margin="0,0,0,30" />

                <Grid RowSpacing="20" ColumnSpacing="15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!-- Basic Plan -->
                    <Frame Grid.Column="0"
                           BackgroundColor="#ECF0F1"
                           Padding="20"
                           CornerRadius="15"
                           HasShadow="True">
                        <StackLayout Spacing="15">
                            <Label Text="Basic"
                                   FontSize="22"
                                   FontAttributes="Bold"
                                   TextColor="#2C3E50"
                                   HorizontalOptions="Center" />
                            <Label Text="$29"
                                   FontSize="36"
                                   FontAttributes="Bold"
                                   TextColor="#E74C3C"
                                   HorizontalOptions="Center" />
                            <Label Text="per month"
                                   FontSize="14"
                                   TextColor="#7F8C8D"
                                   HorizontalOptions="Center" />
                            <StackLayout Spacing="8">
                                <Label Text="• 100 Business Cards" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• 5 Flyer Designs" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• Basic Support" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• 3-Day Delivery" FontSize="14" TextColor="#2C3E50" />
                            </StackLayout>
                            <Button Text="Choose Plan"
                                    BackgroundColor="#3498DB"
                                    TextColor="White"
                                    CornerRadius="20"
                                    Clicked="OnBasicPlanClicked" />
                        </StackLayout>
                    </Frame>

                    <!-- Professional Plan -->
                    <Frame Grid.Column="1"
                           BackgroundColor="#E8F5E8"
                           Padding="20"
                           CornerRadius="15"
                           HasShadow="True"
                           BorderColor="#27AE60"
                           BorderWidth="2">
                        <StackLayout Spacing="15">
                            <Label Text="Professional"
                                   FontSize="22"
                                   FontAttributes="Bold"
                                   TextColor="#2C3E50"
                                   HorizontalOptions="Center" />
                            <Label Text="$59"
                                   FontSize="36"
                                   FontAttributes="Bold"
                                   TextColor="#E74C3C"
                                   HorizontalOptions="Center" />
                            <Label Text="per month"
                                   FontSize="14"
                                   TextColor="#7F8C8D"
                                   HorizontalOptions="Center" />
                            <StackLayout Spacing="8">
                                <Label Text="• 500 Business Cards" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• 20 Flyer Designs" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• Priority Support" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• 1-Day Delivery" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• Custom Design" FontSize="14" TextColor="#2C3E50" />
                            </StackLayout>
                            <Button Text="Choose Plan"
                                    BackgroundColor="#27AE60"
                                    TextColor="White"
                                    CornerRadius="20"
                                    Clicked="OnProfessionalPlanClicked" />
                        </StackLayout>
                    </Frame>

                    <!-- Enterprise Plan -->
                    <Frame Grid.Column="2"
                           BackgroundColor="#ECF0F1"
                           Padding="20"
                           CornerRadius="15"
                           HasShadow="True">
                        <StackLayout Spacing="15">
                            <Label Text="Enterprise"
                                   FontSize="22"
                                   FontAttributes="Bold"
                                   TextColor="#2C3E50"
                                   HorizontalOptions="Center" />
                            <Label Text="$99"
                                   FontSize="36"
                                   FontAttributes="Bold"
                                   TextColor="#E74C3C"
                                   HorizontalOptions="Center" />
                            <Label Text="per month"
                                   FontSize="14"
                                   TextColor="#7F8C8D"
                                   HorizontalOptions="Center" />
                            <StackLayout Spacing="8">
                                <Label Text="• Unlimited Prints" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• Unlimited Designs" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• 24/7 Support" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• Same Day Delivery" FontSize="14" TextColor="#2C3E50" />
                                <Label Text="• Dedicated Manager" FontSize="14" TextColor="#2C3E50" />
                            </StackLayout>
                            <Button Text="Choose Plan"
                                    BackgroundColor="#8E44AD"
                                    TextColor="White"
                                    CornerRadius="20"
                                    Clicked="OnEnterprisePlanClicked" />
                        </StackLayout>
                    </Frame>
                </Grid>
            </StackLayout>

            <!-- Contact Section -->
            <StackLayout Padding="30,40" BackgroundColor="#34495E">
                <Label Text="Contact Us"
                       FontSize="28"
                       FontAttributes="Bold"
                       TextColor="White"
                       HorizontalOptions="Center"
                       Margin="0,0,0,30" />

                <Grid RowSpacing="20" ColumnSpacing="20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!-- Contact Info -->
                    <StackLayout Grid.Column="0" Spacing="20">
                        <StackLayout Orientation="Horizontal" Spacing="15">
                            <Image Source="phone_icon.png" HeightRequest="24" WidthRequest="24" />
                            <StackLayout>
                                <Label Text="Phone" FontSize="16" FontAttributes="Bold" TextColor="White" />
                                <Label Text="+****************" FontSize="14" TextColor="#BDC3C7" />
                            </StackLayout>
                        </StackLayout>

                        <StackLayout Orientation="Horizontal" Spacing="15">
                            <Image Source="email_icon.png" HeightRequest="24" WidthRequest="24" />
                            <StackLayout>
                                <Label Text="Email" FontSize="16" FontAttributes="Bold" TextColor="White" />
                                <Label Text="<EMAIL>" FontSize="14" TextColor="#BDC3C7" />
                            </StackLayout>
                        </StackLayout>

                        <StackLayout Orientation="Horizontal" Spacing="15">
                            <Image Source="location_icon.png" HeightRequest="24" WidthRequest="24" />
                            <StackLayout>
                                <Label Text="Address" FontSize="16" FontAttributes="Bold" TextColor="White" />
                                <Label Text="123 Print Street, Design City, DC 12345" FontSize="14" TextColor="#BDC3C7" />
                            </StackLayout>
                        </StackLayout>

                        <StackLayout Orientation="Horizontal" Spacing="15">
                            <Image Source="clock_icon.png" HeightRequest="24" WidthRequest="24" />
                            <StackLayout>
                                <Label Text="Business Hours" FontSize="16" FontAttributes="Bold" TextColor="White" />
                                <Label Text="Mon-Fri: 8AM-6PM" FontSize="14" TextColor="#BDC3C7" />
                                <Label Text="Sat: 9AM-4PM" FontSize="14" TextColor="#BDC3C7" />
                            </StackLayout>
                        </StackLayout>
                    </StackLayout>

                    <!-- Quick Contact Form -->
                    <StackLayout Grid.Column="1" Spacing="15">
                        <Label Text="Quick Contact" FontSize="18" FontAttributes="Bold" TextColor="White" />

                        <Entry x:Name="NameEntry"
                               Placeholder="Your Name"
                               BackgroundColor="White"
                               TextColor="#2C3E50" />

                        <Entry x:Name="EmailEntry"
                               Placeholder="Your Email"
                               Keyboard="Email"
                               BackgroundColor="White"
                               TextColor="#2C3E50" />

                        <Entry x:Name="PhoneEntry"
                               Placeholder="Your Phone"
                               Keyboard="Telephone"
                               BackgroundColor="White"
                               TextColor="#2C3E50" />

                        <Editor x:Name="MessageEditor"
                                Placeholder="Your Message"
                                HeightRequest="100"
                                BackgroundColor="White"
                                TextColor="#2C3E50" />

                        <Button Text="Send Message"
                                BackgroundColor="#E74C3C"
                                TextColor="White"
                                CornerRadius="20"
                                Clicked="OnSendMessageClicked" />
                    </StackLayout>
                </Grid>
            </StackLayout>

            <!-- Online Ordering Section -->
            <StackLayout Padding="30,40" BackgroundColor="#E8F5E8">
                <Label Text="Online Ordering System"
                       FontSize="28"
                       FontAttributes="Bold"
                       TextColor="#2C3E50"
                       HorizontalOptions="Center"
                       Margin="0,0,0,20" />

                <Label Text="Upload your designs, choose specifications, and place orders 24/7"
                       FontSize="16"
                       TextColor="#7F8C8D"
                       HorizontalOptions="Center"
                       HorizontalTextAlignment="Center"
                       Margin="0,0,0,30" />

                <Grid ColumnSpacing="20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <Button Grid.Column="0"
                            Text="Upload Design"
                            BackgroundColor="#3498DB"
                            TextColor="White"
                            CornerRadius="20"
                            Clicked="OnUploadDesignClicked" />

                    <Button Grid.Column="1"
                            Text="Track Order"
                            BackgroundColor="#F39C12"
                            TextColor="White"
                            CornerRadius="20"
                            Clicked="OnTrackOrderClicked" />

                    <Button Grid.Column="2"
                            Text="Reorder"
                            BackgroundColor="#27AE60"
                            TextColor="White"
                            CornerRadius="20"
                            Clicked="OnReorderClicked" />
                </Grid>
            </StackLayout>

            <!-- Footer -->
            <StackLayout Padding="30,20" BackgroundColor="#2C3E50">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <StackLayout Grid.Column="0">
                        <Label Text="© 2024 United Printing Services"
                               FontSize="14"
                               TextColor="#BDC3C7" />
                        <Label Text="All rights reserved"
                               FontSize="12"
                               TextColor="#95A5A6" />
                    </StackLayout>

                    <StackLayout Grid.Column="1" Orientation="Horizontal" HorizontalOptions="End" Spacing="15">
                        <Button Text="Privacy"
                                BackgroundColor="Transparent"
                                TextColor="#BDC3C7"
                                FontSize="14"
                                Clicked="OnPrivacyClicked" />
                        <Button Text="Terms"
                                BackgroundColor="Transparent"
                                TextColor="#BDC3C7"
                                FontSize="14"
                                Clicked="OnTermsClicked" />
                        <Button Text="Support"
                                BackgroundColor="Transparent"
                                TextColor="#BDC3C7"
                                FontSize="14"
                                Clicked="OnSupportClicked" />
                    </StackLayout>
                </Grid>
            </StackLayout>

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
